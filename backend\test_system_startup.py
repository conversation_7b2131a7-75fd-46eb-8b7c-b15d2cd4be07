#!/usr/bin/env python3
"""
Test khởi động hệ thống Multi-Agent Medical Chatbot

File này kiểm tra tất cả các thành phần ch<PERSON>h của hệ thống để đảm bảo
hệ thống có thể khởi động thành công.

Chạy test: python test_system_startup.py
"""

import os
import sys
import traceback
import time
from typing import Dict, List, Any
import requests
import threading
from pathlib import Path

# Thêm thư mục backend vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SystemStartupTest:
    """Class để test khởi động hệ thống"""
    
    def __init__(self):
        self.test_results = {}
        self.errors = []
        self.warnings = []
        
    def log_result(self, test_name: str, success: bool, message: str = "", error: str = ""):
        """<PERSON>hi lại kết quả test"""
        self.test_results[test_name] = {
            "success": success,
            "message": message,
            "error": error
        }
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if error:
            print(f"   Error: {error}")
            self.errors.append(f"{test_name}: {error}")
            
    def log_warning(self, message: str):
        """Ghi lại cảnh báo"""
        print(f"⚠️  WARNING: {message}")
        self.warnings.append(message)

    def test_environment_variables(self):
        """Test các biến môi trường cần thiết"""
        print("\n🔍 Testing Environment Variables...")
        
        required_vars = [
            "GOOGLE_API_KEY",
            "TAVILY_API_KEY", 
            "AZURE_SPEECH_KEY",
            "AZURE_SPEECH_REGION"
        ]
        
        optional_vars = [
            "ELEVEN_LABS_API_KEY",
            "HUGGINGFACE_TOKEN",
            "QDRANT_URL",
            "QDRANT_API_KEY"
        ]
        
        missing_required = []
        missing_optional = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_required.append(var)
                
        for var in optional_vars:
            if not os.getenv(var):
                missing_optional.append(var)
        
        if missing_required:
            self.log_result(
                "Environment Variables", 
                False, 
                f"Missing required variables: {', '.join(missing_required)}"
            )
        else:
            self.log_result(
                "Environment Variables", 
                True, 
                "All required environment variables found"
            )
            
        if missing_optional:
            self.log_warning(f"Missing optional variables: {', '.join(missing_optional)}")

    def test_config_initialization(self):
        """Test khởi tạo cấu hình"""
        print("\n🔍 Testing Configuration Initialization...")
        
        try:
            from config import Config
            config = Config()
            
            # Kiểm tra các config chính
            assert hasattr(config, 'agent_decision')
            assert hasattr(config, 'conversation')
            assert hasattr(config, 'rag')
            assert hasattr(config, 'medical_cv')
            assert hasattr(config, 'web_search')
            assert hasattr(config, 'api')
            assert hasattr(config, 'speech')
            assert hasattr(config, 'validation')
            assert hasattr(config, 'ui')
            
            self.log_result(
                "Config Initialization", 
                True, 
                "Configuration loaded successfully"
            )
            return config
            
        except Exception as e:
            self.log_result(
                "Config Initialization", 
                False, 
                "Failed to initialize configuration",
                str(e)
            )
            return None

    def test_required_directories(self):
        """Test tạo các thư mục cần thiết"""
        print("\n🔍 Testing Required Directories...")
        
        required_dirs = [
            "uploads/backend",
            "uploads/frontend", 
            "uploads/skin_lesion_output",
            "uploads/speech",
            "data"
        ]
        
        try:
            for directory in required_dirs:
                os.makedirs(directory, exist_ok=True)
                
            self.log_result(
                "Required Directories", 
                True, 
                "All required directories created/verified"
            )
            
        except Exception as e:
            self.log_result(
                "Required Directories", 
                False, 
                "Failed to create required directories",
                str(e)
            )

    def test_model_files(self):
        """Test sự tồn tại của các file model"""
        print("\n🔍 Testing Model Files...")
        
        model_files = [
            "./agents/image_analysis_agent/brain_tumor_agent/models/brain_tumor_segmentation.pth",
            "./agents/image_analysis_agent/chest_xray_agent/models/covid_chest_xray_model.pth", 
            "./agents/image_analysis_agent/skin_lesion_agent/models/checkpointN25_.pth.tar"
        ]
        
        missing_models = []
        for model_file in model_files:
            if not os.path.exists(model_file):
                missing_models.append(model_file)
                
        if missing_models:
            self.log_warning(f"Missing model files: {missing_models}")
            self.log_result(
                "Model Files", 
                True, 
                "Some model files missing but system can still start"
            )
        else:
            self.log_result(
                "Model Files", 
                True, 
                "All model files found"
            )

    def test_agent_initialization(self, config):
        """Test khởi tạo các agents"""
        print("\n🔍 Testing Agent Initialization...")
        
        if not config:
            self.log_result(
                "Agent Initialization", 
                False, 
                "Cannot test agents without config"
            )
            return
            
        try:
            # Test RAG Agent
            from agents.rag_agent import MedicalRAG
            rag_agent = MedicalRAG(config)
            self.log_result(
                "RAG Agent", 
                True, 
                "RAG Agent initialized successfully"
            )
            
        except Exception as e:
            self.log_result(
                "RAG Agent", 
                False, 
                "Failed to initialize RAG Agent",
                str(e)
            )
            
        try:
            # Test Web Search Agent
            from agents.web_search_processor_agent import WebSearchProcessorAgent
            web_agent = WebSearchProcessorAgent(config)
            self.log_result(
                "Web Search Agent", 
                True, 
                "Web Search Agent initialized successfully"
            )
            
        except Exception as e:
            self.log_result(
                "Web Search Agent", 
                False, 
                "Failed to initialize Web Search Agent",
                str(e)
            )
            
        try:
            # Test Image Analysis Agent
            from agents.image_analysis_agent import ImageAnalysisAgent
            image_agent = ImageAnalysisAgent(config)
            self.log_result(
                "Image Analysis Agent", 
                True, 
                "Image Analysis Agent initialized successfully"
            )
            
        except Exception as e:
            self.log_result(
                "Image Analysis Agent", 
                False, 
                "Failed to initialize Image Analysis Agent",
                str(e)
            )

    def test_fastapi_app_creation(self):
        """Test tạo FastAPI app"""
        print("\n🔍 Testing FastAPI App Creation...")
        
        try:
            from app import app
            
            # Kiểm tra các endpoint chính
            routes = [route.path for route in app.routes]
            required_endpoints = [
                "/health",
                "/api/chat", 
                "/api/upload",
                "/api/validate",
                "/api/transcribe",
                "/api/generate-speech"
            ]
            
            missing_endpoints = []
            for endpoint in required_endpoints:
                if endpoint not in routes:
                    missing_endpoints.append(endpoint)
                    
            if missing_endpoints:
                self.log_result(
                    "FastAPI App", 
                    False, 
                    f"Missing endpoints: {missing_endpoints}"
                )
            else:
                self.log_result(
                    "FastAPI App", 
                    True, 
                    "FastAPI app created with all required endpoints"
                )
                
        except Exception as e:
            self.log_result(
                "FastAPI App", 
                False, 
                "Failed to create FastAPI app",
                str(e)
            )

    def test_dependencies(self):
        """Test các dependencies cần thiết"""
        print("\n🔍 Testing Dependencies...")
        
        required_packages = [
            "fastapi",
            "uvicorn", 
            "langchain",
            "langchain_google_genai",
            "qdrant_client",
            "requests",
            "pydub",
            "python-dotenv"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)
                
        if missing_packages:
            self.log_result(
                "Dependencies", 
                False, 
                f"Missing packages: {missing_packages}"
            )
        else:
            self.log_result(
                "Dependencies", 
                True, 
                "All required dependencies found"
            )

    def run_all_tests(self):
        """Chạy tất cả các test"""
        print("🚀 Starting System Startup Tests...")
        print("=" * 50)
        
        start_time = time.time()
        
        # Chạy các test theo thứ tự
        self.test_environment_variables()
        self.test_dependencies()
        config = self.test_config_initialization()
        self.test_required_directories()
        self.test_model_files()
        self.test_agent_initialization(config)
        self.test_fastapi_app_creation()
        
        end_time = time.time()
        
        # Tổng kết kết quả
        self.print_summary(end_time - start_time)
        
    def print_summary(self, duration: float):
        """In tổng kết kết quả test"""
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Warnings: {len(self.warnings)} ⚠️")
        print(f"Duration: {duration:.2f} seconds")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! System is ready to start.")
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Please check the errors above.")
            
        if self.warnings:
            print(f"\n⚠️  {len(self.warnings)} warning(s) found:")
            for warning in self.warnings:
                print(f"   - {warning}")

def main():
    """Hàm main để chạy test"""
    tester = SystemStartupTest()
    tester.run_all_tests()
    
    # Trả về exit code dựa trên kết quả test
    failed_tests = sum(1 for result in tester.test_results.values() if not result["success"])
    sys.exit(failed_tests)

if __name__ == "__main__":
    main()
