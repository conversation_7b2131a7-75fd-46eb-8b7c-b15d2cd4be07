#!/usr/bin/env python3
"""
Quick Startup Test - <PERSON><PERSON><PERSON> tra nhanh khởi động hệ thống

File test đơn giản để kiểm tra nhanh các thành phần cơ bản
của hệ thống medical chatbot có thể khởi động được không.

Chạy test: python quick_startup_test.py
"""

import os
import sys
from pathlib import Path

def check_environment():
    """Kiểm tra môi trường cơ bản"""
    print("🔍 Checking environment...")
    
    # Kiểm tra Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # Kiểm tra file .env
    if os.path.exists('.env'):
        print("✅ .env file found")
    else:
        print("⚠️  .env file not found - create one with required API keys")
    
    return True

def check_basic_imports():
    """Kiểm tra import các package c<PERSON> bản"""
    print("\n🔍 Checking basic imports...")
    
    basic_packages = [
        ("os", "Built-in"),
        ("sys", "Built-in"), 
        ("json", "Built-in"),
        ("fastapi", "FastAPI"),
        ("uvicorn", "ASGI Server"),
        ("dotenv", "Environment Variables"),
        ("requests", "HTTP Client")
    ]
    
    all_good = True
    for package, description in basic_packages:
        try:
            if package == "dotenv":
                from dotenv import load_dotenv
            else:
                __import__(package)
            print(f"✅ {package} ({description})")
        except ImportError:
            print(f"❌ {package} ({description}) - Missing!")
            all_good = False
    
    return all_good

def check_config():
    """Kiểm tra config cơ bản"""
    print("\n🔍 Checking configuration...")
    
    try:
        from config import Config
        config = Config()
        print("✅ Configuration loaded")
        
        # Kiểm tra một số config cơ bản
        if hasattr(config, 'api') and hasattr(config.api, 'host'):
            print(f"✅ API config: {config.api.host}:{config.api.port}")
        else:
            print("⚠️  API config incomplete")
            
        return True
        
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        return False

def check_directories():
    """Kiểm tra và tạo thư mục cần thiết"""
    print("\n🔍 Checking directories...")
    
    required_dirs = [
        "uploads",
        "uploads/backend",
        "uploads/frontend", 
        "uploads/skin_lesion_output",
        "uploads/speech",
        "data"
    ]
    
    for directory in required_dirs:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}")
        except Exception as e:
            print(f"❌ {directory}: {e}")
            return False
    
    return True

def check_app_creation():
    """Kiểm tra tạo FastAPI app"""
    print("\n🔍 Checking FastAPI app creation...")
    
    try:
        from app import app
        print("✅ FastAPI app created")
        
        # Kiểm tra một số endpoint cơ bản
        routes = [route.path for route in app.routes]
        if "/health" in routes:
            print("✅ Health endpoint found")
        if "/api/chat" in routes:
            print("✅ Chat endpoint found")
            
        return True
        
    except Exception as e:
        print(f"❌ FastAPI app creation failed: {e}")
        return False

def check_agent_decision():
    """Kiểm tra agent decision module"""
    print("\n🔍 Checking agent decision module...")
    
    try:
        from agents.agent_decision import process_query
        print("✅ Agent decision module loaded")
        return True
        
    except Exception as e:
        print(f"❌ Agent decision module failed: {e}")
        return False

def main():
    """Hàm main để chạy quick test"""
    print("🚀 Quick Startup Test for Medical Chatbot")
    print("=" * 45)
    
    tests = [
        ("Environment", check_environment),
        ("Basic Imports", check_basic_imports),
        ("Configuration", check_config),
        ("Directories", check_directories),
        ("FastAPI App", check_app_creation),
        ("Agent Decision", check_agent_decision)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️  {test_name} test had issues")
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 45)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 45)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All quick tests passed! System should start successfully.")
        print("\nTo start the system:")
        print("  python app.py")
        print("  or")
        print("  uvicorn app:app --host 0.0.0.0 --port 8000")
        return 0
    else:
        print(f"⚠️  {total - passed} test(s) failed. Please fix issues before starting.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
