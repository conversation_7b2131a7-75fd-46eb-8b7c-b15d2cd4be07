# Hướng dẫn Test Khởi động Hệ thống Medical Chatbot

Tà<PERSON> liệu này hướng dẫn cách sử dụng các file test để kiểm tra và khởi động hệ thống Medical Chatbot một cách an toàn.

## 📁 Các File Test

### 1. `quick_startup_test.py` - Test Nhanh
**M<PERSON>c đích**: <PERSON><PERSON><PERSON> tra nhanh các thành phần cơ bản của hệ thống

**Cách chạy**:
```bash
cd backend
python quick_startup_test.py
```

**Kiểm tra**:
- ✅ Python version (>= 3.8)
- ✅ File .env tồn tại
- ✅ Import các package cơ bản
- ✅ Cấu hình hệ thống
- ✅ Tạo thư mục cần thiết
- ✅ FastAPI app
- ✅ Agent decision module

### 2. `test_system_startup.py` - Test Chi tiết
**<PERSON><PERSON><PERSON> đích**: <PERSON><PERSON><PERSON> tra chi tiết tất cả các thành phần của hệ thống

**<PERSON><PERSON><PERSON> chạy**:
```bash
cd backend
python test_system_startup.py
```

**Kiểm tra**:
- 🔍 Environment Variables (API keys)
- 🔍 Dependencies (tất cả packages)
- 🔍 Configuration (chi tiết config)
- 🔍 Required Directories
- 🔍 Model Files (AI models)
- 🔍 Agent Initialization (tất cả agents)
- 🔍 FastAPI App (tất cả endpoints)

### 3. `start_system.py` - Khởi động An toàn
**Mục đích**: Khởi động hệ thống với kiểm tra an toàn

**Cách chạy**:
```bash
cd backend
python start_system.py
```

**Tùy chọn**:
```bash
# Chế độ development (mặc định)
python start_system.py

# Chế độ production
python start_system.py --production

# Xem hướng dẫn
python start_system.py --help
```

## 🔧 Chuẩn bị Trước khi Test

### 1. Cài đặt Dependencies
```bash
cd backend
pip install -r requirements.txt
```

### 2. Tạo file .env
Tạo file `.env` trong thư mục `backend` với nội dung:

```env
# Required API Keys
GOOGLE_API_KEY=your_google_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here

# Optional API Keys
ELEVEN_LABS_API_KEY=your_eleven_labs_key_here
HUGGINGFACE_TOKEN=your_huggingface_token_here
QDRANT_URL=your_qdrant_url_here
QDRANT_API_KEY=your_qdrant_api_key_here

# API Configuration
API_BASE_URL=http://localhost:8000
```

### 3. Chuẩn bị Model Files (Tùy chọn)
Nếu bạn có các model files, đặt chúng vào:
- `agents/image_analysis_agent/brain_tumor_agent/models/brain_tumor_segmentation.pth`
- `agents/image_analysis_agent/chest_xray_agent/models/covid_chest_xray_model.pth`
- `agents/image_analysis_agent/skin_lesion_agent/models/checkpointN25_.pth.tar`

## 🚀 Quy trình Khởi động Khuyến nghị

### Bước 1: Test Nhanh
```bash
python quick_startup_test.py
```
Nếu tất cả test pass, chuyển sang bước 2.

### Bước 2: Test Chi tiết (Tùy chọn)
```bash
python test_system_startup.py
```
Để kiểm tra chi tiết hơn các thành phần.

### Bước 3: Khởi động Hệ thống
```bash
python start_system.py
```

## 📊 Hiểu Kết quả Test

### Ký hiệu
- ✅ **PASS**: Test thành công
- ❌ **FAIL**: Test thất bại - cần sửa
- ⚠️ **WARNING**: Cảnh báo - hệ thống vẫn có thể chạy

### Các Lỗi Thường gặp

#### 1. Missing API Keys
```
❌ Environment Variables: Missing required variables: GOOGLE_API_KEY
```
**Giải pháp**: Thêm API key vào file `.env`

#### 2. Missing Dependencies
```
❌ Dependencies: Missing packages: langchain_google_genai
```
**Giải pháp**: 
```bash
pip install langchain_google_genai
```

#### 3. Configuration Error
```
❌ Config Initialization: Failed to initialize configuration
```
**Giải pháp**: Kiểm tra file `config.py` và `.env`

#### 4. Model Files Missing
```
⚠️ Missing model files: [brain_tumor_segmentation.pth]
```
**Giải pháp**: Tải model files hoặc bỏ qua (hệ thống vẫn chạy được)

## 🔧 Troubleshooting

### Nếu Quick Test Fail
1. Kiểm tra Python version: `python --version`
2. Kiểm tra file .env tồn tại
3. Cài đặt lại dependencies: `pip install -r requirements.txt`

### Nếu System Startup Fail
1. Chạy quick test trước
2. Kiểm tra log chi tiết
3. Đảm bảo port 8000 không bị chiếm

### Nếu Server Không Start
1. Kiểm tra port đã được sử dụng: `netstat -an | grep 8000`
2. Thử port khác: Sửa trong `config.py`
3. Kiểm tra firewall

## 📝 Logs và Debug

### Xem Logs Chi tiết
```bash
python start_system.py 2>&1 | tee startup.log
```

### Debug Mode
Mặc định hệ thống chạy ở debug mode với auto-reload.

### Production Mode
```bash
python start_system.py --production
```

## 🌐 Sau khi Khởi động Thành công

Hệ thống sẽ chạy tại:
- **API**: http://localhost:8000
- **Health Check**: http://localhost:8000/health
- **API Documentation**: http://localhost:8000/docs
- **Interactive API**: http://localhost:8000/redoc

### Test API
```bash
# Health check
curl http://localhost:8000/health

# Chat endpoint
curl -X POST http://localhost:8000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"query": "Hello"}'
```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Chạy `python test_system_startup.py` để xem chi tiết
2. Kiểm tra file `.env` và `config.py`
3. Đảm bảo tất cả dependencies đã được cài đặt
4. Kiểm tra logs trong terminal

---

**Lưu ý**: Các file test này được thiết kế để giúp bạn khởi động hệ thống một cách an toàn và phát hiện sớm các vấn đề tiềm ẩn.
