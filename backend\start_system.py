#!/usr/bin/env python3
"""
System Startup Script - Script khởi động hệ thống an toàn

Script này sẽ:
1. <PERSON>ể<PERSON> tra các điều kiện cần thiết trước khi khởi động
2. Khởi động hệ thống medical chatbot
3. <PERSON> dõi trạng thái hệ thống

Chạy: python start_system.py
"""

import os
import sys
import time
import signal
import subprocess
from pathlib import Path
from typing import Optional

class SystemStarter:
    """Class để khởi động hệ thống an toàn"""
    
    def __init__(self):
        self.server_process: Optional[subprocess.Popen] = None
        
    def check_prerequisites(self) -> bool:
        """Kiểm tra các điều kiện tiên quyết"""
        print("🔍 Checking prerequisites...")
        
        # Kiểm tra Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ is required")
            return False
        
        # Kiểm tra file cấu hình
        if not os.path.exists('config.py'):
            print("❌ config.py not found")
            return False
            
        # Kiểm tra file .env
        if not os.path.exists('.env'):
            print("⚠️  .env file not found")
            print("   Please create .env file with required API keys:")
            print("   - GOOGLE_API_KEY=your_google_api_key")
            print("   - TAVILY_API_KEY=your_tavily_api_key")
            print("   - AZURE_SPEECH_KEY=your_azure_speech_key")
            print("   - AZURE_SPEECH_REGION=your_azure_region")
            return False
        
        # Kiểm tra các package cần thiết
        required_packages = ['fastapi', 'uvicorn', 'langchain_google_genai']
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                print(f"❌ Missing package: {package}")
                print(f"   Install with: pip install {package}")
                return False
        
        print("✅ All prerequisites met")
        return True
    
    def setup_directories(self):
        """Tạo các thư mục cần thiết"""
        print("📁 Setting up directories...")
        
        directories = [
            "uploads/backend",
            "uploads/frontend",
            "uploads/skin_lesion_output", 
            "uploads/speech",
            "data",
            "data/qdrant_db",
            "data/docs_db",
            "data/parsed_docs"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
        print("✅ Directories created")
    
    def test_configuration(self) -> bool:
        """Test cấu hình hệ thống"""
        print("⚙️  Testing configuration...")
        
        try:
            from config import Config
            config = Config()
            
            # Kiểm tra API keys
            if not os.getenv('GOOGLE_API_KEY'):
                print("❌ GOOGLE_API_KEY not set")
                return False
                
            if not os.getenv('TAVILY_API_KEY'):
                print("⚠️  TAVILY_API_KEY not set - web search may not work")
                
            print("✅ Configuration valid")
            return True
            
        except Exception as e:
            print(f"❌ Configuration error: {e}")
            return False
    
    def start_server(self, host: str = "0.0.0.0", port: int = 8000, debug: bool = True):
        """Khởi động server"""
        print(f"🚀 Starting server on {host}:{port}...")
        
        try:
            # Import app để kiểm tra
            from app import app
            
            # Khởi động server với uvicorn
            cmd = [
                sys.executable, "-m", "uvicorn", 
                "app:app",
                "--host", host,
                "--port", str(port)
            ]
            
            if debug:
                cmd.append("--reload")
                
            print(f"Command: {' '.join(cmd)}")
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Đợi server khởi động
            print("⏳ Waiting for server to start...")
            time.sleep(3)
            
            # Kiểm tra server có chạy không
            if self.server_process.poll() is None:
                print("✅ Server started successfully!")
                print(f"🌐 Access the API at: http://{host}:{port}")
                print(f"📋 Health check: http://{host}:{port}/health")
                print(f"📚 API docs: http://{host}:{port}/docs")
                return True
            else:
                print("❌ Server failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def monitor_server(self):
        """Theo dõi server"""
        if not self.server_process:
            return
            
        print("\n📊 Server is running. Press Ctrl+C to stop.")
        print("=" * 50)
        
        try:
            # Đọc output từ server
            for line in iter(self.server_process.stdout.readline, ''):
                if line:
                    print(line.rstrip())
                    
                # Kiểm tra server còn chạy không
                if self.server_process.poll() is not None:
                    break
                    
        except KeyboardInterrupt:
            print("\n🛑 Stopping server...")
            self.stop_server()
        except Exception as e:
            print(f"❌ Error monitoring server: {e}")
    
    def stop_server(self):
        """Dừng server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("✅ Server stopped gracefully")
            except subprocess.TimeoutExpired:
                print("⚠️  Force killing server...")
                self.server_process.kill()
                self.server_process.wait()
                print("✅ Server force stopped")
            except Exception as e:
                print(f"❌ Error stopping server: {e}")
    
    def run(self, host: str = "0.0.0.0", port: int = 8000, debug: bool = True):
        """Chạy toàn bộ quá trình khởi động"""
        print("🏥 Medical Chatbot System Starter")
        print("=" * 40)
        
        # Kiểm tra điều kiện tiên quyết
        if not self.check_prerequisites():
            print("❌ Prerequisites not met. Please fix issues and try again.")
            return False
        
        # Tạo thư mục
        self.setup_directories()
        
        # Test cấu hình
        if not self.test_configuration():
            print("❌ Configuration invalid. Please fix and try again.")
            return False
        
        # Khởi động server
        if not self.start_server(host, port, debug):
            print("❌ Failed to start server.")
            return False
        
        # Theo dõi server
        self.monitor_server()
        
        return True

def signal_handler(signum, frame):
    """Xử lý signal để dừng server"""
    print("\n🛑 Received stop signal")
    sys.exit(0)

def main():
    """Hàm main"""
    # Đăng ký signal handler
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Parse arguments đơn giản
    host = "0.0.0.0"
    port = 8000
    debug = True
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--production":
            debug = False
        elif sys.argv[1] == "--help":
            print("Usage: python start_system.py [--production] [--help]")
            print("  --production: Run in production mode (no auto-reload)")
            print("  --help: Show this help message")
            return
    
    # Khởi động hệ thống
    starter = SystemStarter()
    success = starter.run(host, port, debug)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
